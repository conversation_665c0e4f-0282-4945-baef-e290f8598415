# PactCRM

PactCRM - SaaS-платформа для застройщиков, продающих недвижимость в рассрочку.

## Структура проекта

Проект организован как монорепозиторий с использованием Turborepo и состоит из следующих модулей:

- **TenantDashboard** - панель управления для арендаторов (застройщиков)
- **SuperAdmin** - панель администратора платформы
- **ClientApplication** - клиентское приложение для покупателей недвижимости

## Технологический стек

- **Frontend**: Next.js, React, TypeScript, Tailwind CSS, shadcn/ui
- **Backend**: Supabase (PostgreSQL, Auth, Storage, Functions)
- **Инфраструктура**: Vercel, GitHub Actions

## Начало работы

### Предварительные требования

- Node.js 18+
- PNPM 8+

### Установка

```bash
# Клонирование репозитория
git clone https://github.com/KLASTER-DIGITAL/pactcrm.git
cd pactcrm

# Установка зависимостей
pnpm install

# Запуск в режиме разработки
pnpm dev
```

## Структура монорепозитория

```
pactcrm/
├── apps/
│   ├── tenant-dashboard/    # Панель управления для арендаторов
│   ├── super-admin/         # Панель администратора платформы
│   └── client-app/          # Клиентское приложение
├── packages/
│   ├── ui/                  # Общие UI компоненты
│   ├── eslint-config-custom/ # Общая конфигурация ESLint
│   ├── tsconfig/            # Общая конфигурация TypeScript
│   └── supabase-client/     # Клиент для работы с Supabase
└── ...
```

## Команды

- `pnpm build` - Сборка всех приложений и пакетов
- `pnpm dev` - Запуск всех приложений в режиме разработки
- `pnpm lint` - Проверка кода линтером
- `pnpm format` - Форматирование кода

## Соглашения по коду

- Используем Conventional Commits для сообщений коммитов
- Следуем принципам Feature-Sliced Design для организации кода
- Используем TypeScript для типизации
- Следуем принципам атомарного дизайна для UI компонентов
