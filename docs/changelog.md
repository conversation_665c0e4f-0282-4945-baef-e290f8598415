# Changelog проекта PactCRM

Все значимые изменения в проекте документируются в этом файле.

Формат основан на [Keep a Changelog](https://keepachangelog.com/ru/1.0.0/),
и проект придерживается [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [2024-05-11] - Разработка пользовательского интерфейса для шаблонов договоров

### Добавлено
- Созданы страницы для работы с шаблонами договоров и их версиями
- Реализован компонент ContractTemplatesList для отображения списка шаблонов с фильтрацией и пагинацией
- Реализован компонент ContractTemplateForm для создания и редактирования шаблонов
- Реализован компонент ContractTemplateDetails для просмотра детальной информации о шаблоне
- Реализован компонент ContractTemplateVersionsList для отображения списка версий шаблона
- Реализован компонент ContractTemplateVersionForm для создания и редактирования версий шаблона
- Реализован компонент ContractTemplateVersionDetails для просмотра детальной информации о версии шаблона
- Реализован компонент TemplateVariablesEditor для редактирования переменных шаблона
- Реализован компонент TemplateFileUploader для загрузки файлов шаблона

### Изменено
- Обновлена структура навигации для добавления раздела шаблонов договоров
- Улучшена организация компонентов с разделением ответственности

## [2024-05-10] - Разработка системы шаблонов договоров

### Добавлено
- Создана миграция для таблиц шаблонов договоров (contract_templates, contract_template_versions, contract_template_files, contract_documents)
- Реализованы типы данных для работы с шаблонами договоров
- Создан API для работы с шаблонами договоров, их версиями и документами
- Реализованы хуки useContractTemplates, useContractTemplateVersions и useContractDocuments
- Добавлена возможность загрузки файлов шаблонов в хранилище Supabase
- Реализована система версионирования шаблонов договоров
- Добавлена возможность генерации документов на основе шаблонов с подстановкой данных

### Изменено
- Обновлена структура типов данных для поддержки шаблонов договоров
- Расширена система разрешений для работы с шаблонами договоров
- Улучшена организация кода с разделением на модули по функциональности

## [2024-05-10] - Разработка модуля управления договорами

### Добавлено
- Создан API для работы с договорами в пакете supabase-client
- Реализованы хуки useContractsList и useContract для работы с договорами
- Создан компонент ContractsList для отображения списка договоров с фильтрацией и пагинацией
- Создан компонент ContractForm для создания и редактирования договоров
- Создан компонент ContractDetails для просмотра детальной информации о договоре
- Добавлены страницы для создания, просмотра и редактирования договоров
- Реализована интеграция с системой ролей и разрешений (RBAC)

### Изменено
- Обновлена страница списка договоров для работы с реальными данными
- Улучшена структура компонентов с разделением ответственности
- Добавлены утилиты для форматирования дат и денежных сумм

## [2023-12-02] - Создание базовой структуры маршрутизации и аутентификации

### Добавлено
- Создана базовая структура маршрутизации для tenant-dashboard
- Реализованы страницы для основных модулей: dashboard, properties, clients, contracts, payments, settings
- Создан клиентский дашборд с основными функциями
- Реализован middleware для проверки аутентификации и перенаправления пользователей
- Настроена защита маршрутов на основе ролей пользователей

### Изменено
- Обновлены компоненты для работы с Next.js App Router
- Доработан пакет supabase-client для интеграции с App Router
- Улучшена структура проекта для более четкого разделения модулей

### Исправлено
- Устранены проблемы с маршрутизацией и перенаправлением пользователей
- Исправлены ошибки в компонентах аутентификации

## [2023-12-01] - Настройка базовой инфраструктуры

### Добавлено
- Создан проект Supabase "pactCRM"
- Спроектирована и реализована схема базы данных
- Настроена мультитенантная архитектура с Row-Level Security (RLS)
- Созданы основные таблицы: tenants, users, roles, permissions, clients, contracts, properties, buildings, apartments и др.
- Настроены связи между таблицами и ограничения целостности данных

### Изменено
- Доработан пакет supabase-client для работы с новой схемой базы данных
- Обновлены типы данных в соответствии с реализованной схемой

### Исправлено
- Устранены проблемы с типизацией данных в supabase-client

## [2024-05-03] - Исправление тестов для системы ролей и разрешений

### Исправлено
- Исправлены тесты для RoleContext в пакете supabase-client
- Устранены проблемы с обновлением роли пользователя в тестах
- Улучшена структура тестов для более надежного тестирования контекста ролей
- Исправлены ошибки в моках для тестирования AuthContext и TenantContext

## [2024-05-02] - Завершение реализации системы ролей и разрешений (RBAC)

### Добавлено
- Реализовано начальное заполнение базы данных ролями и разрешениями
- Добавлены функции базы данных для работы с ролями и разрешениями
- Создана подробная документация по системе RBAC в project.md
- Реализованы компоненты для проверки разрешений на уровне UI

### Изменено
- Доработаны компоненты RoleContext, PermissionGuard и RoleBasedRoute
- Обновлены экспорты компонентов в пакете supabase-client
- Улучшена система проверки разрешений с учетом иерархии ролей

## [2024-05-01] - Начало реализации системы ролей и разрешений (RBAC)

### Добавлено
- Создана система ролей и разрешений (RBAC) с поддержкой мультитенантности
- Добавлены таблицы roles, permissions, role_permissions, user_roles
- Реализованы хранимые процедуры для работы с ролями и разрешениями
- Созданы компоненты RoleManager и UserRoleManager для управления ролями
- Добавлена страница управления ролями и разрешениями в панель управления

## [Неопубликовано]

### Добавлено
- Начальная структура документации проекта
- Файл глобальной архитектуры проекта (`Project.md`)
- Трекер задач (`Tasktracker.md`)
- Технический журнал (`Diary.md`)
- Список вопросов по архитектуре (`qa.md`)
- Документация по модулям:
  - Tenant Dashboard
  - SuperAdmin Panel
  - Client Application
  - AI Integration Layer
  - Notification System
  - External Integrations

## [2023-11-15] - Обновление инфраструктуры проекта

### Добавлено
- Настроена система автоматического обновления документации при коммитах
- Созданы скрипты для обновления changelog.md, tasktracker.md и project.md
- Добавлены Git hooks для автоматического обновления документации

### Изменено
- Улучшена структура проекта с использованием Turborepo
- Исправлена проблема с динамическим импортом React в UI компонентах

## [0.1.0] - 2023-10-01

### Добавлено
- Инициализация проекта
- Базовая концепция архитектуры
- Выбор технологического стека (Supabase, shadcn-admin, React Native)
- Определение основных компонентов системы
- Разработка системы ролей и взаимодействий

### Изменено
- Уточнение требований к мультитенантности
- Корректировка подхода к изоляции данных

### Исправлено
- Устранение противоречий в первоначальной концепции
- Уточнение границ модулей системы
